# AI Enhancement Setup Guide

This guide explains how to set up and use the Google Gemini AI integration for Vietnamese text enhancement.

## Prerequisites

1. **Google AI Studio Account**: Sign up at [Google AI Studio](https://makersuite.google.com/)
2. **API Key**: Generate an API key from the Google AI Studio dashboard
3. **Python Dependencies**: All required packages should be installed via `pip install -r requirements.txt`

## Setup Steps

### 1. Get Your Google AI API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### 2. Configure Environment Variables

**Option A: Using .env file (Recommended)**
```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your API key
GOOGLE_AI_API_KEY=your_actual_api_key_here
```

**Option B: Set environment variable directly**
```bash
# Windows (Command Prompt)
set GOOGLE_AI_API_KEY=your_actual_api_key_here

# Windows (PowerShell)
$env:GOOGLE_AI_API_KEY="your_actual_api_key_here"

# Linux/macOS
export GOOGLE_AI_API_KEY="your_actual_api_key_here"
```

### 3. Test AI Enhancement

```bash
# Test the AI enhancer directly
python ai_enhancer.py

# Test with the converter
python excel_to_files.py test_chapters.xlsx --format md --ai-enhance
```

## Usage Examples

### Basic AI Enhancement
```bash
python excel_to_files.py your_file.xlsx --format md --ai-enhance
```

### Advanced Usage
```bash
# With custom output directory and verbose logging
python excel_to_files.py chapters.xlsx \
  --format md \
  --ai-enhance \
  --output-dir enhanced_chapters \
  --verbose
```

## AI Enhancement Features

### What the AI Does
- **Improves Vietnamese writing style** according to `format.md` guidelines
- **Converts modern Vietnamese** to classical/ancient literary style
- **Standardizes character titles** and relationships (ta, ngươi, hắn, nàng, etc.)
- **Enhances vocabulary** with appropriate Hán-Việt terms
- **Maintains original meaning** while improving readability

### What the AI Preserves
- ✅ **Original plot and content**
- ✅ **Character names and places**
- ✅ **Chapter structure**
- ✅ **Dialogue and narrative flow**
- ✅ **Technical terms and proper nouns**

## Error Handling

The system includes comprehensive error handling:

### No API Key
```
⚠️ Failed to initialize AI enhancer: Google AI API key not found
   Proceeding without AI enhancement
```
**Solution**: Set up your `GOOGLE_AI_API_KEY` environment variable

### Wrong Format
```
⚠️ AI enhancement is only available for Markdown format (--format md)
   Proceeding without AI enhancement...
```
**Solution**: Use `--format md` with `--ai-enhance`

### API Rate Limits
The system automatically handles rate limits with:
- Exponential backoff retry logic
- Configurable delays between requests
- Maximum retry attempts (default: 3)

### Network Issues
If AI enhancement fails for individual chapters:
```
⚠️ AI enhancement failed for: Chapter Name... (using original)
```
The system will save the original content and continue processing.

## Performance Considerations

### Processing Time
- **Without AI**: ~1-2 seconds per chapter
- **With AI**: ~5-10 seconds per chapter (depending on content length)
- **Progress Bar**: Shows real-time progress for AI processing

### API Costs
- Google Gemini API has usage-based pricing
- Typical cost: ~$0.001-0.01 per chapter (varies by content length)
- Monitor your usage in Google AI Studio dashboard

### Optimization Tips
1. **Batch Processing**: Process multiple chapters in one run
2. **Content Length**: Shorter chapters process faster
3. **Network**: Stable internet connection recommended

## Troubleshooting

### Common Issues

**1. Import Error**
```
ModuleNotFoundError: No module named 'google.generativeai'
```
**Solution**: `pip install -r requirements.txt`

**2. API Key Invalid**
```
Error: Invalid API key
```
**Solution**: Verify your API key in Google AI Studio

**3. Quota Exceeded**
```
Error: Quota exceeded
```
**Solution**: Check your API usage limits in Google AI Studio

**4. Network Timeout**
```
Error: Request timeout
```
**Solution**: Check internet connection, system will retry automatically

### Getting Help

1. **Check Logs**: Use `--verbose` flag for detailed logging
2. **Test Components**: Run `python ai_enhancer.py` to test AI setup
3. **Verify Setup**: Ensure API key is correctly set
4. **Check Format**: Confirm using `--format md` with `--ai-enhance`

## Advanced Configuration

You can customize AI behavior by modifying `ai_enhancer.py`:

```python
# Adjust these parameters in the GeminiTextEnhancer class
self.rate_limit_delay = 1.0  # Delay between API calls
self.max_retries = 3         # Maximum retry attempts
model_name = "gemini-1.5-flash"  # AI model to use
```

## Security Notes

- **Keep API Key Secret**: Never commit API keys to version control
- **Use .env Files**: Store sensitive data in environment variables
- **Monitor Usage**: Regularly check your API usage and costs
- **Rotate Keys**: Periodically regenerate API keys for security
