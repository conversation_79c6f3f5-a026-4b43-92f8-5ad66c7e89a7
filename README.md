# Excel to Files Converter with AI Enhancement

A Python tool that converts Excel files containing chapter data into individual text files. Designed with Vietnamese text support, robust filename handling, and optional AI-powered text enhancement using Google Gemini.

## Features

- ✅ **Vietnamese Text Support**: Full UTF-8 encoding support for Vietnamese characters
- ✅ **AI Text Enhancement**: Google Gemini AI integration for improving Vietnamese writing style
- ✅ **Filename Sanitization**: Automatically handles invalid filename characters
- ✅ **Duplicate Handling**: Manages duplicate chapter names by appending numbers
- ✅ **Multiple Output Formats**: Supports both `.txt` and `.md` (Markdown) formats
- ✅ **Progress Tracking**: Visual progress bars for AI processing
- ✅ **Error Handling**: Comprehensive error handling with detailed feedback
- ✅ **CLI Interface**: Easy-to-use command-line interface
- ✅ **Cross-Platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.7+
- pandas
- openpyxl
- click
- google-generativeai (for AI enhancement)
- python-dotenv
- tqdm

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. **For AI Enhancement** (optional):
   - Get a Google AI Studio API key from: https://makersuite.google.com/app/apikey
   - Copy `.env.example` to `.env` and add your API key:
     ```bash
     cp .env.example .env
     # Edit .env and add your GOOGLE_AI_API_KEY
     ```

## Excel File Format

Your Excel file must contain at least these two columns:

| Column Name | Description |
|-------------|-------------|
| `chapters` | Chapter names/titles |
| `chapter-content` | Chapter content/text |

### Example Excel Structure:
```
| chapters                   | chapter-content                           |
|----------------------------|-------------------------------------------|
| Chương 1: Giới thiệu      | Nội dung chương 1 với tiếng Việt...      |
| Chương 2: Lịch sử         | Nội dung chương 2 với tiếng Việt...      |
```

## Usage

### Basic Usage
```bash
python excel_to_files.py your_excel_file.xlsx
```

### Advanced Usage
```bash
# Specify output directory
python excel_to_files.py your_excel_file.xlsx --output-dir my_chapters

# Use Markdown format
python excel_to_files.py your_excel_file.xlsx --format md

# Enable AI text enhancement (Markdown only)
python excel_to_files.py your_excel_file.xlsx --format md --ai-enhance

# Combine all options
python excel_to_files.py your_excel_file.xlsx --output-dir chapters --format md --ai-enhance --verbose
```

### Command Line Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--output-dir` | `-o` | Output directory for generated files | `output` |
| `--format` | `-f` | Output format: `txt` or `md` | `txt` |
| `--ai-enhance` | `--ai` | Enable AI text enhancement (Markdown only) | `False` |
| `--encoding` | | Text encoding for output files | `utf-8` |
| `--verbose` | `-v` | Enable verbose logging | `False` |

## AI Text Enhancement

### Overview
When using `--ai-enhance` with Markdown format, the tool leverages Google Gemini AI to improve Vietnamese writing style and quality according to the guidelines in `format.md`.

### Features
- **Vietnamese Writing Style**: Improves grammar, vocabulary, and flow
- **Ancient/Classical Style**: Converts modern Vietnamese to classical literary style
- **Character Consistency**: Maintains proper character titles and relationships
- **Content Preservation**: Enhances style without changing plot or meaning

### Requirements
1. Set `GOOGLE_AI_API_KEY` environment variable
2. Use `--format md` (AI enhancement only works with Markdown)
3. Add `--ai-enhance` flag

### Example
```bash
# Set your API key
export GOOGLE_AI_API_KEY="your_api_key_here"

# Run with AI enhancement
python excel_to_files.py chapters.xlsx --format md --ai-enhance
```

## Output

### Text Format (`.txt`)
```
Chapter Title
=============

Chapter content goes here...
```

### Markdown Format (`.md`)
```markdown
# Chapter Title

Chapter content goes here...
```

### AI Enhanced Markdown (`.md` with `--ai-enhance`)
```markdown
# Chapter Title

[Enhanced Vietnamese content with improved writing style,
classical vocabulary, and proper character relationships]
```

## Filename Handling

### Sanitization
Invalid filename characters are automatically replaced:
- `< > : " | ? * \ /` → `_` (underscore)
- Leading/trailing spaces and dots are removed
- Long filenames are truncated to 200 characters
- Empty names become "untitled"

### Duplicate Handling
When duplicate chapter names are found:
- First occurrence: `chapter_name.txt`
- Second occurrence: `chapter_name_2.txt`
- Third occurrence: `chapter_name_3.txt`
- And so on...

## Example Workflow

1. **Create a sample Excel file** (optional):
   ```bash
   python create_sample_excel.py
   ```

2. **Convert the Excel file**:
   ```bash
   python excel_to_files.py sample_chapters.xlsx
   ```

3. **Check the output**:
   ```bash
   ls output/
   ```

## Error Handling

The converter handles various error scenarios:

- ❌ **Missing Excel file**: Clear error message with file path
- ❌ **Missing required columns**: Lists available columns
- ❌ **Empty rows**: Skips rows with missing chapter names or content
- ❌ **File write errors**: Reports specific files that failed to write
- ❌ **Invalid Excel format**: Provides detailed error information

## Vietnamese Text Support

This tool is specifically designed to handle Vietnamese text properly:

- Uses UTF-8 encoding for all file operations
- Preserves Vietnamese diacritics (á, à, ả, ã, ạ, etc.)
- Handles Vietnamese characters in both chapter names and content
- Maintains proper text formatting and line breaks

## Troubleshooting

### Common Issues

1. **"Missing required columns" error**:
   - Ensure your Excel file has columns named exactly `chapter` and `chapter-content`
   - Check for extra spaces in column names

2. **Vietnamese characters not displaying correctly**:
   - Ensure your text editor supports UTF-8 encoding
   - Try opening files with a different text editor

3. **Permission errors**:
   - Check that you have write permissions in the output directory
   - Try running with administrator/sudo privileges if needed

4. **Excel file not found**:
   - Verify the file path is correct
   - Use absolute paths if relative paths don't work

### Getting Help

Run the help command to see all available options:
```bash
python excel_to_files.py --help
```

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.
